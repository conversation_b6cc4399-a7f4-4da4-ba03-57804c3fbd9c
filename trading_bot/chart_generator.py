"""
Chart generation and visualization functions
"""

import pandas as pd
import numpy as np
import matplotlib
# Set backend to <PERSON>gg to avoid GUI warnings and work in any thread
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import mplfinance as mpf
from datetime import datetime
import os
from typing import Dict, List, Optional, Tuple, Any
import logging

from .config import *

logger = logging.getLogger(__name__)

class ChartGenerator:
    """Generate and export trading charts"""

    def __init__(self, data_manager):
        self.data_manager = data_manager

        # Ensure matplotlib uses non-GUI backend to avoid threading issues
        if matplotlib.get_backend() != 'Agg':
            matplotlib.use('Agg')

        # Create charts directory if it doesn't exist
        os.makedirs(CHARTS_DIR, exist_ok=True)
        os.makedirs(POSITIONS_DIR, exist_ok=True)


    def plot_position_analysis(self, name: str, timeframe: int, position: Dict[str, Any],
                            save_path: Optional[str] = None, addition_title: Optional[str] = '',
                            plot_nb_only: bool = False) -> str:
        """
        Plot position analysis with entry, SL, TP

        Args:
            name: Symbol name
            timeframe: Timeframe
            position: Position data
            save_path: Custom save path (optional)

        Returns:
            str: Path to saved chart
        """
        try:
            df = self.data_manager.df[name][timeframe]
            waves = self.data_manager.waves[name][timeframe]
            latest_checked_index = waves[-1]['checked_index']
            # start from secondary key -2 primary level
            key_history = self.data_manager.history[name][timeframe]['primary_level']
            last_2_key = key_history[-2:]
            start_index = last_2_key[0]['secondary_key_index']
            if position:
                if 'entry_time' in position and position['entry_time'] < start_index:
                    start_index = position['entry_time']
            primary_key = self.data_manager.primary_level[name][timeframe]

            from trading_bot.wave_analysis import find_wave_index_candle_belongs_to
            wave_start_index = find_wave_index_candle_belongs_to(start_index, waves)
            draw_waves = waves[wave_start_index:]
            draw_df = df.loc[waves[wave_start_index]['start_index']:latest_checked_index]
            if len(draw_df) < 150:
                draw_df = df.loc[:latest_checked_index].tail(150)
            lines = []
            for wave in draw_waves:
                start_date = wave['start_index']
                end_date = wave['peak_index']
                start_close = df.loc[start_date]['Close']
                end_close = df.loc[end_date]['Close']
                lines.append([(start_date, start_close), (end_date, end_close)])

            # Handle fill keylevel level
            if primary_key['label'] == 1:
                color_level = (0, 1, 0, 0.1)
            else:
                color_level = (1, 0, 0, 0.1)
            # Handle fill secondary keylevel level

            fill_color_dic_primary = dict(
                y1=df.loc[primary_key['key_level_index']]['Low'],
                y2=df.loc[primary_key['key_level_index']]['High'],
                color=color_level,
                where=draw_df.index.isin(df.loc[primary_key['key_level_index']:primary_key['checked_index']].index)
            )

            fill_color_dic_secondary = dict(
                y1=df.loc[primary_key['secondary_key_index']]['Low'],
                y2=df.loc[primary_key['secondary_key_index']]['High'],
                color=(1, 1, 0, 0.1),  # Adjust the color as needed
                where=draw_df.index.isin(df.loc[primary_key['secondary_key_index']:primary_key['checked_index']].index)
            )
            fill_color_list = [fill_color_dic_primary, fill_color_dic_secondary]

            # Plot sub primary level
            if self.data_manager.sub_primary_level[name][timeframe] and len(self.data_manager.sub_primary_level[name][timeframe]) > 0 and self.data_manager.sub_primary_level[name][timeframe]['key_level_index'] > primary_key['key_level_index']:
                fill_color_list.append(dict(
                    y1=df.loc[self.data_manager.sub_primary_level[name][timeframe]['key_level_index']]['Low'],
                    y2=df.loc[self.data_manager.sub_primary_level[name][timeframe]['key_level_index']]['High'],
                    color=(0.5, 0, 0.5, 0.5),  # Purple
                    where=draw_df.index.isin(df.loc[self.data_manager.sub_primary_level[name][timeframe]['key_level_index']:primary_key['checked_index']].index)
                ))

            # Plot recent level
            if self.data_manager.recent_level[name][timeframe] and len(self.data_manager.recent_level[name][timeframe]) > 0 and self.data_manager.recent_level[name][timeframe]['key_level_index'] > primary_key['key_level_index']:
                fill_color_list.append(dict(
                    y1=df.loc[self.data_manager.recent_level[name][timeframe]['key_level_index']]['Low'],
                    y2=df.loc[self.data_manager.recent_level[name][timeframe]['key_level_index']]['High'],
                    color=(0, 0, 1, 0.5),  # Blue
                    where=draw_df.index.isin(df.loc[self.data_manager.recent_level[name][timeframe]['key_level_index']:primary_key['checked_index']].index)
                ))

            if df is None or len(df) == 0:
                logger.error(f"No data available for {name} {timeframe}")
                return None
            if position and 'entry_time' in position:
                timestamp = position['entry_time']
                type_chart = f"position_{position['id']}"
            else:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                type_chart = 'level'
            filename = f"{name}_{type_chart}_{timestamp}_{timeframe}"
            if position and 'profit' in position:
                if position['profit'] > 0:
                    filename = f"{filename}_win.png"
                else:
                    filename = f"{filename}_lose.png"
            else:
                filename = f"{filename}.png"
            # Generate filename
            if not plot_nb_only and save_path is None:
                save_path = os.path.join(CHARTS_DIR, filename)
            else:
                save_path = os.path.join(save_path, filename)
            # Create horizontal lines for position levels
            hlines = []
            colors = []
            linestyles = []
            vlines = []

            # Find position entry point in dataframe
            entry_index = None
            if position:
                if 'entry_price' in position:
                    hlines.append(position['entry_price'])
                    colors.append('blue')
                    linestyles.append('-')

                if 'stop_loss' in position:
                    hlines.append(position['stop_loss'])
                    colors.append('red')
                    linestyles.append('--')

                if 'take_profit' in position:
                    hlines.append(position['take_profit'])
                    colors.append('green')
                    linestyles.append('--')

                # Create vertical line for entry time
                if 'entry_time' in position and position['entry_time'] in draw_df.index:
                    vlines.append(position['entry_time'])

            # Plot configuration
            plot_config = {
                'type': 'candle',
                'style': 'charles',
                'title': f'{name} {timeframe}min - {addition_title}',
                'fill_between': fill_color_list,
                'volume': True,
                'figsize': (CHART_WIDTH/CHART_DPI, CHART_HEIGHT/CHART_DPI),
                'warn_too_much_data': 10000000000
            }
            if lines:
                plot_config['alines'] = dict(
                    alines=lines,
                    colors=['b','r','c','k','g'],
                    alpha=[0.35],
                    linewidths=[1.5]
                )

            if not plot_nb_only:
                plot_config['savefig'] = dict(fname=save_path, dpi=CHART_DPI, bbox_inches='tight')

            # Add horizontal lines
            if hlines:
                plot_config['hlines'] = dict(
                    hlines=hlines,
                    colors=colors,
                    linestyle=linestyles
                )

            # Add vertical lines
            if vlines:
                plot_config['vlines'] = dict(
                    vlines=vlines,
                    colors=['black'],
                    linestyle=['-']
                )

            # Generate the plot
            mpf.plot(draw_df, **plot_config)

            # Close the plot to free memory and prevent GUI issues
            plt.close('all')

            logger.info(f"Position chart saved to: {save_path}")
            return save_path

        except Exception as e:
            logger.error(f"Error plotting position chart: {e}")
            return None

    def _create_wave_lines(self, draw_df: pd.DataFrame, waves: List[Dict], level: Dict) -> List[Tuple]:
        """Create lines for wave visualization"""
        try:
            lines = []

            # Add wave lines
            for i, wave in enumerate(waves[-5:]):  # Show last 5 waves
                try:
                    start_idx = wave['start_index']
                    peak_idx = wave['peak_index']

                    if start_idx in draw_df.index and peak_idx in draw_df.index:
                        lines.append((start_idx, peak_idx))
                except Exception as e:
                    logger.debug(f"Error adding wave line {i}: {e}")
                    continue

            # Add level lines
            if level:
                try:
                    if 'key_level_index' in level and 'peak_index' in level:
                        key_idx = level['key_level_index']
                        peak_idx = level['peak_index']

                        if key_idx in draw_df.index and peak_idx in draw_df.index:
                            lines.append((key_idx, peak_idx))
                except Exception as e:
                    logger.debug(f"Error adding level line: {e}")

            return lines

        except Exception as e:
            logger.error(f"Error creating wave lines: {e}")
            return []

    def _create_horizontal_lines(self, level: Dict, position: Optional[Dict]) -> Optional[Dict]:
        """Create horizontal lines for levels and position"""
        try:
            hlines = []
            colors = []
            linestyles = []

            # Add position lines
            if position:
                if 'entry' in position:
                    hlines.append(position['entry'])
                    colors.append('blue')
                    linestyles.append('-')

                if 'sl' in position:
                    hlines.append(position['sl'])
                    colors.append('red')
                    linestyles.append('--')

                if 'tp' in position:
                    hlines.append(position['tp'])
                    colors.append('green')
                    linestyles.append('--')

            if hlines:
                return dict(
                    hlines=hlines,
                    colors=colors,
                    linestyle=linestyles
                )

            return None

        except Exception as e:
            logger.error(f"Error creating horizontal lines: {e}")
            return None

    def _create_vertical_lines(self, position: Optional[Dict]) -> Optional[Dict]:
        """Create vertical lines for important events"""
        try:
            vlines = []

            if position and 'entry_at' in position:
                vlines.append(position['entry_at'])

            if vlines:
                return dict(
                    vlines=vlines,
                    colors=['black'],
                    linestyle=['-']
                )

            return None

        except Exception as e:
            logger.error(f"Error creating vertical lines: {e}")
            return None

    def create_backtest_summary_chart(self, results: List[Dict], save_path: Optional[str] = None) -> str:
        """Create backtest summary chart"""
        try:
            if not results:
                logger.error("No backtest results to plot")
                return None

            # Generate filename
            if save_path is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"backtest_summary_{timestamp}.png"
                save_path = os.path.join(CHARTS_DIR, filename)

            # Create figure with subplots
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

            # Extract data
            profits = [r.get('profit', 0) for r in results]
            cumulative_profits = np.cumsum(profits)
            win_rates = [r.get('win_rate', 0) for r in results]
            trade_counts = [r.get('trade_count', 0) for r in results]

            # Plot 1: Cumulative P&L
            ax1.plot(cumulative_profits, 'b-', linewidth=2)
            ax1.set_title('Cumulative P&L')
            ax1.set_xlabel('Trade Number')
            ax1.set_ylabel('Cumulative Profit')
            ax1.grid(True, alpha=0.3)

            # Plot 2: Win Rate
            ax2.plot(win_rates, 'g-', linewidth=2)
            ax2.set_title('Win Rate Over Time')
            ax2.set_xlabel('Trade Number')
            ax2.set_ylabel('Win Rate (%)')
            ax2.grid(True, alpha=0.3)

            # Plot 3: Profit Distribution
            ax3.hist(profits, bins=20, alpha=0.7, color='purple')
            ax3.set_title('Profit Distribution')
            ax3.set_xlabel('Profit')
            ax3.set_ylabel('Frequency')
            ax3.grid(True, alpha=0.3)

            # Plot 4: Trade Count
            ax4.bar(range(len(trade_counts)), trade_counts, alpha=0.7, color='orange')
            ax4.set_title('Trades per Period')
            ax4.set_xlabel('Period')
            ax4.set_ylabel('Trade Count')
            ax4.grid(True, alpha=0.3)

            plt.tight_layout()
            plt.savefig(save_path, dpi=CHART_DPI, bbox_inches='tight')
            plt.close()

            logger.info(f"Backtest summary chart saved to: {save_path}")
            return save_path

        except Exception as e:
            logger.error(f"Error creating backtest summary chart: {e}")
            return None
