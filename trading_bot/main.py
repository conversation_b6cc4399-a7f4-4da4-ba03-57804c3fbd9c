"""
Main trading bot application
"""

import logging
import time
import signal
import sys
from datetime import datetime
from typing import Dict
import pandas as pd

from .config import *
from .data_manager import DataManager
from .strategy_engine import StrategyEngine
from .chart_generator import ChartGenerator
from .binance_client import BinanceWebSocketClient, BinanceDataFetcher, BinanceFuturesClient, create_binance_hook
from .trade_engine import TradeEngine
from .telegram_notifier import TelegramNotifier

# Create logs directory if it doesn't exist
import os
os.makedirs(LOGS_DIR, exist_ok=True)

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'{LOGS_DIR}/trading_bot.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class TradingBot:
    """Main trading bot class"""

    def __init__(self, trade_mode):
        # Initialize Telegram notifier
        self.telegram_notifier = TelegramNotifier()

        self.data_manager = DataManager(trade_mode, self.telegram_notifier)
        self.strategy_engine = StrategyEngine(self.data_manager)
        self.chart_generator = ChartGenerator(self.data_manager)
        self.trade_mode = trade_mode

        # Initialize Binance Futures client for live trading
        self.binance_futures_client = None
        if trade_mode == "live" and BINANCE_FUTURES_ENABLED:
            self._initialize_binance_futures_client()

        # Initialize trade engine with Binance client and Telegram notifier
        self.trade_engine = TradeEngine(
            self.data_manager,
            self.strategy_engine,
            trade_mode,
            self.binance_futures_client,
            self.telegram_notifier
        )

        self.ws_client = None
        self.is_running = False

        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

        # Create directories
        import os
        os.makedirs(DATA_DIR, exist_ok=True)
        os.makedirs(CHARTS_DIR, exist_ok=True)
        os.makedirs(LOGS_DIR, exist_ok=True)

    def _initialize_binance_futures_client(self):
        """Initialize Binance Futures API client"""
        try:
            # Get API credentials from config or environment variables
            import os as os_module
            api_key = os_module.environ.get('BINANCE_API_KEY', BINANCE_FUTURES_API_KEY)
            api_secret = os_module.environ.get('BINANCE_API_SECRET', BINANCE_FUTURES_API_SECRET)

            if not api_key or not api_secret:
                logger.warning("Binance API credentials not found. API trading will be disabled.")
                logger.info("Set BINANCE_API_KEY and BINANCE_API_SECRET environment variables or update config.py")
                return

            # Create Binance Futures client
            self.binance_futures_client = BinanceFuturesClient(
                api_key=api_key,
                api_secret=api_secret,
                testnet=BINANCE_FUTURES_TESTNET
            )

            logger.info(f"Binance Futures client initialized (testnet={BINANCE_FUTURES_TESTNET})")

            # Validate credentials
            if self._validate_api_credentials():
                logger.info("API credentials validated successfully")
            else:
                logger.error("Failed to validate API credentials")
                self.binance_futures_client = None

        except Exception as e:
            logger.error(f"Error initializing Binance Futures client: {e}")
            self.binance_futures_client = None

    def _validate_api_credentials(self) -> bool:
        """Validate Binance API credentials"""
        try:
            if not self.binance_futures_client:
                return False

            # Try to get account info
            account_info = self.binance_futures_client.get_account_info()
            if account_info:
                balance = self.binance_futures_client.get_usdt_balance()
                logger.info(f"API validation successful. USDT Balance: {balance}")
                return True
            else:
                logger.error("Failed to get account info")
                return False

        except Exception as e:
            logger.error(f"Error validating API credentials: {e}")
            return False

    def initialize_data(self, symbol: str = "BTCUSD", limit_rows: int = MAX_ROW_DF):
        """Initialize historical data"""
        try:
            logger.info(f"Initializing data for {symbol}")

            # Load local data
            for timeframe in TIMEFRAMES:
                success = self.data_manager.load_data(symbol, timeframe, limit_rows, self.trade_mode)
                if not success:
                    raise Exception(f"Failed to load data for {symbol} {timeframe}")

            logger.info("Data initialization completed")
            return True

        except Exception as e:
            logger.error(f"Error initializing data: {e}")
            return False

    def _fetch_historical_data(self, symbol: str):
        """Fetch historical data from Binance"""
        try:
            logger.info(f"Fetching historical data for {symbol}")

            fetcher = BinanceDataFetcher()

            # Convert symbol format (BTCUSD -> BTCUSDT)
            binance_symbol = symbol + "T" if symbol.endswith("USD") else symbol

            # Fetch data for scanwave timeframes
            for timeframe in TIMEFRAMES:
                klines = fetcher.fetch_historical_klines(binance_symbol, f"{timeframe}m", 1000)
                # Convert klines to dataframe and save to data_manager
                df = pd.DataFrame(klines)
                df.set_index('Open time', inplace=True)

                # Remove last candle not close yet
                df = df[:-1]

                self.data_manager.df[symbol][timeframe] = df
                logger.info(f"  Fetched {len(klines)} {timeframe}m candles for {symbol}")

        except Exception as e:
            logger.error(f"Error fetching historical data: {e}")



    def start_live_trading(self, symbol: str = "BTCUSD", strategy_name = None):
        """Start live trading with WebSocket connection"""
        try:
            logger.info(f"Starting live trading for {symbol} with strategy: {strategy_name}")

            # Set strategy name in data manager
            self.data_manager.strategy_name = strategy_name

            # Fetch historical data 1m for live mode:
            self._fetch_historical_data(symbol)

            # Initialize data first
            if not self.initialize_data(symbol):
                logger.error("Failed to initialize data")
                return False

            # Update all data to latest timestamp
            self.data_manager.update_all_data_to_new_timestamp(symbol)

            # Save charts level after initialize data
            # for timeframe in TIMEFRAMES:
            #     chart_path = self.chart_generator.plot_position_analysis(symbol, timeframe, None)
            #     if chart_path:
            #         logger.info(f"Chart saved after initialize data: {chart_path}")

            # Create Binance hook with live trade engine integration
            binance_hook = create_binance_hook(
                self.data_manager,
                self.strategy_engine,
                self.chart_generator,
                self.trade_engine
            )

            # Convert symbol format for Binance
            binance_symbol = symbol + "T" if symbol.endswith("USD") else symbol

            # Create WebSocket client
            self.ws_client = BinanceWebSocketClient(binance_hook)

            # Connect to WebSocket
            if not self.ws_client.connect(binance_symbol):
                logger.error("Failed to connect to Binance WebSocket")
                return False

            self.is_running = True
            logger.info("Live trading started successfully")

            # Keep the bot running
            self._run_main_loop()

            return True

        except Exception as e:
            logger.error(f"Error starting live trading: {e}")
            return False

    def _run_main_loop(self):
        """Main loop for the trading bot"""
        try:
            logger.info("Entering main trading loop")

            while self.is_running:
                try:
                    # Check WebSocket connection
                    if self.ws_client and not self.ws_client.is_alive():
                        logger.warning("WebSocket connection lost, attempting to reconnect...")
                        # Reconnection is handled automatically by the WebSocket client

                    # Log memory usage periodically
                    if hasattr(self, '_last_memory_log'):
                        if time.time() - self._last_memory_log > 300:  # Every 5 minutes
                            self._log_memory_usage()
                            self._last_memory_log = time.time()
                    else:
                        self._last_memory_log = time.time()

                    # Sleep to prevent high CPU usage
                    time.sleep(1)

                except KeyboardInterrupt:
                    logger.info("Received keyboard interrupt")
                    break
                except Exception as e:
                    logger.error(f"Error in main loop: {e}")
                    time.sleep(5)  # Wait before continuing

            logger.info("Main trading loop ended")

        except Exception as e:
            logger.error(f"Error in main loop: {e}")

    def _log_memory_usage(self):
        """Log current memory usage"""
        try:
            memory_info = self.data_manager.get_memory_usage()
            total_memory = 0

            for symbol, timeframes in memory_info.items():
                for tf, info in timeframes.items():
                    total_memory += info['memory_mb']

            logger.info(f"Total memory usage: {total_memory:.2f} MB")

        except Exception as e:
            logger.error(f"Error logging memory usage: {e}")

    def run_backtest(self, strategy_filter: str, symbol: str = "BTCUSD", rows_back: int = MAX_ROW_DF,
                    initial_balance: float = 10000.0, risk_per_trade: float = 0.02) -> bool:
        """
        Run backtest

        Args:
            symbol: Trading symbol
            rows_back: Number of rows (1-minute candles) to backtest from the tail
            initial_balance: Starting balance
            risk_per_trade: Risk percentage per trade
            strategy_filter: Strategy name to test (required)
        """
        try:
            logger.info(f"Starting backtest for {symbol} with strategy: {strategy_filter}")

            # Validate strategy_filter
            if not strategy_filter:
                logger.error("strategy_filter is required for backtest")
                return False

            # Initialize data
            if not self.initialize_data(symbol):
                logger.error("Failed to initialize data")
                return False

            # Get the full 1-minute dataframe
            df_1m = self.data_manager.get_dataframe(symbol, 1)
            if df_1m is None or len(df_1m) == 0:
                logger.error(f"No 1-minute data available for {symbol}")
                return False

            # Get the last N rows (tail)
            df_tail = df_1m.tail(rows_back)
            if len(df_tail) == 0:
                logger.error(f"No data available for the last {rows_back} rows")
                return False

            logger.info(f"Using last {len(df_tail)} rows of 1-minute data for backtest")


            # Run backtest
            result = self.trade_engine.run_backtest(
                symbol=symbol,
                rows_back=rows_back,
                strategy_filter=strategy_filter,
                initial_balance=initial_balance,
                risk_per_trade=risk_per_trade
            )

            # Print results
            self._print_backtest_results(result)

            # Save results
            # timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            # filename = f"backtest_{symbol}_{timestamp}.pkl"
            # self.backtest_engine.save_results(result, filename)

            # Generate summary chart
            # chart_path = self.chart_generator.create_backtest_summary_chart(
            #     [result.to_dict()]
            # )
            # if chart_path:
            #     logger.info(f"Backtest summary chart saved: {chart_path}")

            # Generate end chart df
            # primary_level = self.data_manager.primary_level[symbol][15]
            # chart_path = self.chart_generator.plot_position_analysis(
            #     symbol, 15, None
            # )
            # if chart_path:
            #     logger.info(f"Chart final saved: {chart_path}")

            return True

        except Exception as e:
            logger.error(f"Error running backtest: {e}")
            return False

    def get_live_trading_status(self) -> Dict:
        """Get current live trading status"""
        try:
            return self.trade_engine.get_live_trading_status()
        except Exception as e:
            logger.error(f"Error getting live trading status: {e}")
            return {}

    def set_live_trading_params(self, balance: float = None, risk_per_trade: float = None):
        """Set live trading parameters"""
        try:
            self.trade_engine.set_live_trading_params(balance, risk_per_trade)
        except Exception as e:
            logger.error(f"Error setting live trading params: {e}")

    def close_all_live_trades(self):
        """Close all open live trades"""
        try:
            # Get current price from latest candle
            latest_candle = self.data_manager.get_latest_candle("BTCUSD", 1)
            if latest_candle is not None:
                current_price = latest_candle['Close']
                self.trade_engine.close_all_live_trades(current_price)
            else:
                logger.warning("Cannot close trades: no current price available")
        except Exception as e:
            logger.error(f"Error closing all live trades: {e}")

    def enable_api_trading(self, leverage: int = None):
        """Enable API trading"""
        try:
            if not self.binance_futures_client:
                logger.error("Binance Futures client not initialized")
                return False

            return self.trade_engine.enable_api_trading(leverage)

        except Exception as e:
            logger.error(f"Error enabling API trading: {e}")
            return False

    def disable_api_trading(self):
        """Disable API trading"""
        try:
            self.trade_engine.disable_api_trading()
            logger.info("API trading disabled")
        except Exception as e:
            logger.error(f"Error disabling API trading: {e}")

    def emergency_close_all(self, symbol: str = "BTCUSD"):
        """Emergency close all positions"""
        try:
            logger.warning("EMERGENCY CLOSE ALL REQUESTED")
            return self.trade_engine.emergency_close_all(symbol)
        except Exception as e:
            logger.error(f"Error in emergency close all: {e}")
            return False

    def sync_with_exchange(self, symbol: str = "BTCUSD"):
        """Sync local trades with exchange"""
        try:
            return self.trade_engine.sync_with_exchange(symbol)
        except Exception as e:
            logger.error(f"Error syncing with exchange: {e}")
            return False

    def monitor_orders(self, symbol: str = "BTCUSD"):
        """Monitor open orders"""
        try:
            return self.trade_engine.monitor_open_orders(symbol)
        except Exception as e:
            logger.error(f"Error monitoring orders: {e}")
            return {}

    def scan_wave(self, symbol: str = "BTCUSD", rows: int = MAX_ROW_DF) -> bool:
        """
        Scan and update wave and level data for the last n rows of a symbol

        Args:
            symbol: Trading symbol
            rows: Number of rows to scan from the tail

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.info(f"Starting wave scan for {symbol}, last {rows} rows")

            # Initialize data
            if not self.initialize_data(symbol, rows):
                logger.error("Failed to initialize data")
                return False

            # Update all data to latest timestamp for all timeframes
            logger.info("Updating wave and level data for all timeframes...")
            for timeframe in SCANWAVE_TIMEFRAMES:
                df = self.data_manager.get_dataframe(symbol, timeframe)
                if df is None or len(df) == 0:
                    logger.warning(f"No data available for {symbol} {timeframe}")
                    continue

                # Update to the latest timestamp
                latest_timestamp = df.index[-1]
                logger.info(f"Processing {symbol} {timeframe}m - {len(df)} candles, latest: {latest_timestamp}")
                self.data_manager.update_data_to_new_timestamp(symbol, latest_timestamp, timeframe)

                # Log wave and level info
                waves_count = len(self.data_manager.waves[symbol][timeframe])
                primary_level = self.data_manager.primary_level[symbol][timeframe]
                logger.info(f"  Waves: {waves_count}, Primary level stack: {primary_level.get('stack_level', 0)}")

            # Save wave and level data to file
            save_path = self._save_wave_level_data(symbol)
            if save_path:
                logger.info(f"Wave and level data saved to: {save_path}")
            else:
                logger.error("Failed to save wave and level data")
                return False

            return True

        except Exception as e:
            logger.error(f"Error scanning waves: {e}")
            return False

    def _save_wave_level_data(self, symbol: str) -> str:
        """
        Save wave and level data to a pickle file

        Args:
            symbol: Trading symbol

        Returns:
            str: Path to saved file, or None if failed
        """
        try:
            import pickle
            import os

            # Create directory if it doesn't exist
            os.makedirs(DATA_DIR, exist_ok=True)

            # Prepare data to save
            data_to_save = {
                'symbol': symbol,
                'timestamp': datetime.now().isoformat(),
                'trade_mode': self.trade_mode,
                'timeframes': {},
            }

            # Collect data for each timeframe
            for timeframe in TIMEFRAMES:
                if symbol in self.data_manager.waves and timeframe in self.data_manager.waves[symbol]:
                    data_to_save['timeframes'][timeframe] = {
                        'waves': self.data_manager.waves[symbol][timeframe],
                        'primary_level': self.data_manager.primary_level[symbol][timeframe],
                        'recent_level': self.data_manager.recent_level[symbol][timeframe],
                        'sub_primary_level': self.data_manager.sub_primary_level[symbol][timeframe],
                        'history': self.data_manager.history[symbol][timeframe],
                    }

            # Generate filename with timestamp
            timestamp_str = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{symbol}_wave_level_{self.trade_mode}_{timestamp_str}.pkl"
            filepath = os.path.join(DATA_DIR, filename)

            # Save to pickle file
            with open(filepath, 'wb') as f:
                pickle.dump(data_to_save, f)

            return filepath

        except Exception as e:
            logger.error(f"Error saving wave and level data: {e}")
            return None

    def load_wave_level_data(self, filepath: str) -> bool:
        """
        Load wave and level data from a pickle file

        Args:
            filepath: Path to the pickle file

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            return self.data_manager.load_wave_level_data(filepath)
        except Exception as e:
            logger.error(f"Error loading wave and level data: {e}")
            return False

    def _print_backtest_results(self, result):
        """Print backtest results to console"""
        try:
            print("\n" + "="*50)
            print("BACKTEST RESULTS")
            print("="*50)
            print(f"Begin Balance: ${result.begin_balance:.2f}")
            print(f"End Balance: ${result.end_balance:.2f}")
            print(f"Total Trades: {result.total_trades}")
            print(f"Winning Trades: {result.winning_trades}")
            print(f"Losing Trades: {result.losing_trades}")
            print(f"Win Rate: {result.win_rate:.2f}%")
            print(f"Total Profit: ${result.total_profit:.2f}")
            print(f"Average Win: ${result.avg_win:.2f}")
            print(f"Average Loss: ${result.avg_loss:.2f}")
            print(f"Profit Factor: {result.profit_factor:.2f}")
            print(f"Max Drawdown: ${result.max_drawdown:.2f} ({result.max_drawdown_percent:.2f}%)")
            print(f"Sharpe Ratio: {result.sharpe_ratio:.2f}")
            print(f"Duration: {result.duration_days} days")
            print(f"Reasons: {result.reasons}")
            print("="*50)

        except Exception as e:
            logger.error(f"Error printing backtest results: {e}")

    def stop(self):
        """Stop the trading bot"""
        try:
            logger.info("Stopping trading bot...")

            self.is_running = False

            if self.ws_client:
                self.ws_client.disconnect()

            logger.info("Trading bot stopped")

        except Exception as e:
            logger.error(f"Error stopping trading bot: {e}")

    def _signal_handler(self, signum, frame):
        """Handle system signals for graceful shutdown"""
        logger.info(f"Received signal {signum}, shutting down gracefully...")
        self.stop()
        sys.exit(0)

def main():
    """Main entry point"""
    try:
        import argparse

        parser = argparse.ArgumentParser(description='Trading Bot')
        parser.add_argument('--mode', choices=['live', 'backtest', 'scanwave'], default='live',
                          help='Run mode: live trading, backtest, or scanwave')
        parser.add_argument('--symbol', default='BTCUSD',
                          help='Trading symbol (default: BTCUSD)')
        parser.add_argument('--rows', type=int, default=MAX_ROW_DF,
                          help='Number of 1-minute rows for backtest/scanwave (default: 1000000)')
        parser.add_argument('--balance', type=float, default=10000.0,
                          help='Initial balance for backtest (default: 10000)')
        parser.add_argument('--risk', type=float, default=0.02,
                          help='Risk per trade (default: 0.02)')
        parser.add_argument('--strategy', type=str, required=False,
                          help='Strategy to test (required for backtest mode)')
        parser.add_argument('--list-strategies', action='store_true',
                          help='List all available strategies and exit')

        args = parser.parse_args()

        # Handle list strategies command
        if args.list_strategies:
            data_manager = DataManager(args.mode)
            strategy_engine = StrategyEngine(data_manager)
            strategies = strategy_engine.get_available_strategies()

            print("Available strategies:")
            for i, strategy in enumerate(strategies, 1):
                print(f"  {i}. {strategy}")
            print(f"\nTotal: {len(strategies)} strategies")
            print("\nUsage examples:")
            print(f"  python -m trading_bot.main --mode backtest --strategy {strategies[0]}")
            print("  python -m trading_bot.main --mode live")
            return

        # Create bot instance
        # For scanwave mode, use backtest mode for data loading
        bot_mode = 'backtest' if args.mode == 'scanwave' else args.mode
        bot = TradingBot(bot_mode)

        if args.mode == 'live':
            if not args.strategy:
                logger.error("Strategy is required for live mode. Use --strategy to specify one.")
                logger.info("Use --list-strategies to see all available strategies")
                return
            logger.info("Starting in live trading mode")
            bot.start_live_trading(args.symbol, args.strategy)

        elif args.mode == 'backtest':
            logger.info("Starting in backtest mode")

            # Validate strategy is provided for backtest mode
            if not args.strategy:
                logger.error("Strategy is required for backtest mode. Use --strategy to specify one.")
                logger.info("Use --list-strategies to see all available strategies")
                return

            # Validate strategy exists
            available_strategies = bot.strategy_engine.get_available_strategies()
            if args.strategy not in available_strategies:
                logger.error(f"Strategy '{args.strategy}' not found.")
                logger.info(f"Available strategies: {', '.join(available_strategies)}")
                logger.info("Use --list-strategies to see all available strategies")
                return

            logger.info(f"Testing strategy: {args.strategy}")

            bot.run_backtest(
                strategy_filter=args.strategy,
                symbol=args.symbol,
                rows_back=args.rows,
                initial_balance=args.balance,
                risk_per_trade=args.risk
            )

        elif args.mode == 'scanwave':
            logger.info("Starting in scanwave mode")
            logger.info(f"Scanning waves for {args.symbol}, last {args.rows} rows")

            success = bot.scan_wave(
                symbol=args.symbol,
                rows=args.rows
            )

            if success:
                logger.info("Wave scan completed successfully")
            else:
                logger.error("Wave scan failed")
                sys.exit(1)

    except Exception as e:
        logger.error(f"Error in main: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
