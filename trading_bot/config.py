"""
Configuration settings for the trading bot
"""

# Trading parameters
TIMEFRAMES = [1, 15, 60, 1440]

# Timeframes to scanwave
SCANWAVE_TIMEFRAMES = [15, 60, 1440]

CHECK_SYMBOLS = {
    'BTCUSD': None,
    # 'XAUUSD': 41,
}

# Technical analysis parameters
NUMBER_CAL_MA_CANDLE = 49
MINIMUM_IMBALANCE_RATIO = 1
BIG_CANDLE_RATIO = 2.2
EG_CANDLE_RATIO = 80.0
DOJI_CANDLE_RATIO = 11.0
NUMBER_CANDLES_SIDEWAY = 5
MIN_RR = 1
MIN_POINT_OPEN = 4
RATIO_MA_TP = 2
RATIO_MA_SL = 4
SMALL_CANDLE_RATIO = 1/3
PERCENT_DEFAULT_TP = 0.5
MIN_RR_OPEN_NOW = 2.5
SPACE_BREAK_RATIO = 0.1
LEVEL_BREAK_SPACE_RATIO = 0.5
MAX_CONFIRM_BREAK_RATIO = 11
WEAK_TREND_RATIO = 0.2
LIQUIDITY_RATIO = 1

# Memory optimization
MAX_ROW_DF = 1000000
MAX_HISTORY_SIZE = 1000  # Limit history size
CLEANUP_INTERVAL = 100  # Clean up every N candles

# Backtest settings
MAX_CONCURRENT_TRADES_PER_STRATEGY = 1  # Max concurrent trades per strategy

# Binance API settings
BINANCE_BASE_URL = "https://fapi.binance.com"
BINANCE_TESTNET_BASE_URL = "https://testnet.binancefuture.com"
WEBSOCKET_URL = "wss://fstream.binance.com/ws/"

# Binance Futures API settings
BINANCE_FUTURES_TESTNET = True  # Set to False for production trading
BINANCE_FUTURES_API_KEY = "3nDIx85uZR1X8WTl2lGDPf7iwcZn2IZ2A1LNGzHeyy5y22t0Dc2yVWHLogZ00LxV"  # Set your API key here or via environment variable
BINANCE_FUTURES_API_SECRET = "z0nPhRWevUt4milpszwK31rj0ECvpAOuky1SD7Ir4Xd6jLRX4NWJXwVnbeBM3D6T"  # Set your API secret here or via environment variable
BINANCE_FUTURES_ENABLED = True  # Enable/disable actual order execution

# Trading parameters
DEFAULT_LEVERAGE = 10  # Default leverage for futures trading
MAX_POSITION_SIZE_USDT = 1000  # Maximum position size in USDT
MIN_POSITION_SIZE_USDT = 10  # Minimum position size in USDT
MAX_DAILY_LOSS_USDT = 500  # Maximum daily loss before stopping trading
DRY_RUN_MODE = False  # If True, simulate orders without executing them

# File paths
DATA_DIR = "data"
CHARTS_DIR = "charts"
POSITIONS_DIR = "positions"
LOGS_DIR = "logs"

# Telegram Bot settings
import os
TELEGRAM_BOT_TOKEN = os.getenv('TELEGRAM_BOT_TOKEN', '')  # Set your bot token here or via environment variable
TELEGRAM_CHAT_ID = os.getenv('TELEGRAM_CHAT_ID', '')     # Set your chat ID here or via environment variable
TELEGRAM_ENABLED = bool(TELEGRAM_BOT_TOKEN and TELEGRAM_CHAT_ID)  # Auto-enable if both token and chat ID are set

# Chart settings
CHART_WIDTH = 1920
CHART_HEIGHT = 1080
CHART_DPI = 100

# Location and pattern points
LOCATION_POINT = {
    'top': 1,
    'bot': 2,
    'prebot': 1,
    'below': 3,
    'mid': 1,
    '68': 2,
    'secondary': 2,
}

CANDLES_PATTERN_POINT = {
    'eg': {'point': 4, 'count': 1},
    'mera': {'point': 3, 'count': 1},
    'osb': {'point': 2, 'count': 1},
    'nosd': {'point': 1, 'count': 2},
}

COC_PATTERN_POINT = {
    'correction_broken': {
        'location': ['bot', '68', 'below', 'prebot', 'secondary', 'mid', 'top'],
        'point': 4,
        'entry_now': True,
        'reverse_mode': False,
        'require_ob_tp': False,
        'candle_check': False,
    },
    'zanshin1': {
        'location': ['bot', '68', 'below', 'prebot', 'secondary', 'mid', 'top'],
        'point': 4,
        'entry_now': True,
        'reverse_mode': False,
        'require_ob_tp': False,
        'candle_check': False,
    },
    'zanshin2': {
        'location': ['bot', '68', 'below', 'prebot', 'secondary', 'mid', 'top'],
        'point': 4,
        'entry_now': True,
        'reverse_mode': False,
        'require_ob_tp': False,
        'candle_check': False,
        'max_open_trade': 1,
    },
    'bos': {
        'location': ['bot', '68', 'below', 'prebot'],
        'point': 4,
        'entry_now': True,
        'reverse_mode': False,
        'require_ob_tp': False,
        'candle_check': False,
        'max_open_trade': 1,
    },
    'break_range_reverse': {
        'location': ['bot', 'below', 'prebot'],
        'point': 4,
        'entry_now': True,
        'reverse_mode': False,
        'require_ob_tp': False,
        'candle_check': False,
        'max_open_trade': 1,
    },
    'smc': {
        'location': ['68'],
        'point': 4,
        'entry_now': True,
        'reverse_mode': False,
        'require_ob_tp': False,
        'candle_check': False,
        'max_open_trade': 1,
    },
    'confluent_key': {
        'location': ['68', 'bot', 'prebot'],
        'point': 4,
        'entry_now': True,
        'reverse_mode': False,
        'require_ob_tp': False,
        'candle_check': False,
        'max_open_trade': 1,
    },

    'range_fake_breakout': {
        'location': ['bot', '68', 'below', 'prebot'],
        'point': 4,
        'entry_now': True,
        'reverse_mode': False,
        'require_ob_tp': False,
        'candle_check': False,
    },
}
