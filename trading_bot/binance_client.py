"""
Binance WebSocket client for real-time data streaming
"""

import websocket
import json
import threading
import time, os
import ssl
import urllib3
from datetime import datetime
from typing import Dict, Callable, Optional, List

import logging

from .config import *

logger = logging.getLogger(__name__)

class BinanceWebSocketClient:
    """WebSocket client for Binance real-time data"""

    def __init__(self, on_message_callback: Callable[[Dict], None]):
        self.on_message_callback = on_message_callback
        self.ws = None
        self.is_connected = False
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 5
        self.reconnect_delay = 5  # seconds

    def connect(self, symbol: str = "btcusdt"):
        """Connect to Binance WebSocket stream"""
        try:
            # Convert symbol to lowercase for Binance API
            symbol_lower = symbol.lower()

            # Construct WebSocket URL for 1-minute klines
            url = f"{WEBSOCKET_URL}{symbol_lower}_perpetual@continuousKline_1m"

            logger.info(f"Connecting to Binance WebSocket: {url}")

            # Create SSL context that handles certificate verification properly
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE

            # Create WebSocket connection with SSL context
            self.ws = websocket.WebSocketApp(
                url,
                on_open=self._on_open,
                on_message=self._on_message,
                on_error=self._on_error,
                on_close=self._on_close
            )

            # Start WebSocket in a separate thread with SSL options
            self.ws_thread = threading.Thread(
                target=self.ws.run_forever,
                kwargs={'sslopt': {"cert_reqs": ssl.CERT_NONE}}
            )
            self.ws_thread.daemon = True
            self.ws_thread.start()

            return True

        except Exception as e:
            logger.error(f"Error connecting to Binance WebSocket: {e}")
            return False

    def disconnect(self):
        """Disconnect from WebSocket"""
        try:
            if self.ws:
                self.is_connected = False
                self.ws.close()
                logger.info("Disconnected from Binance WebSocket")
        except Exception as e:
            logger.error(f"Error disconnecting from WebSocket: {e}")

    def _on_open(self, ws):
        """WebSocket connection opened"""
        self.is_connected = True
        self.reconnect_attempts = 0
        logger.info("Binance WebSocket connection opened")

    def _on_message(self, ws, message):
        """Handle incoming WebSocket message"""
        try:
            data = json.loads(message)

            # Check if this is a kline (candlestick) message
            if 'k' in data:
                kline_data = data['k']

                # Only process closed candles
                if kline_data['x']:  # x indicates if the kline is closed
                    candle = self._parse_kline_data(kline_data)
                    logger.info(f"Received candle data: {candle}")
                    # Call the callback function if candle close
                    if self.on_message_callback:
                        self.on_message_callback(candle)

        except Exception as e:
            logger.error(f"Error processing WebSocket message: {e}")

    def _on_error(self, ws, error):
        """Handle WebSocket error"""
        logger.error(f"Binance WebSocket error: {error}")
        self.is_connected = False

    def _on_close(self, ws, close_status_code, close_msg):
        """Handle WebSocket close"""
        self.is_connected = False
        logger.warning(f"Binance WebSocket closed: {close_status_code} - {close_msg}")

        # Attempt to reconnect
        if self.reconnect_attempts < self.max_reconnect_attempts:
            self.reconnect_attempts += 1
            logger.info(f"Attempting to reconnect ({self.reconnect_attempts}/{self.max_reconnect_attempts})...")
            time.sleep(self.reconnect_delay)
            self._reconnect()
        else:
            logger.error("Max reconnection attempts reached. Giving up.")

    def _reconnect(self):
        """Attempt to reconnect"""
        try:
            if self.ws:
                self.ws.run_forever(sslopt={"cert_reqs": ssl.CERT_NONE})
        except Exception as e:
            logger.error(f"Error during reconnection: {e}")

    def _parse_kline_data(self, kline_data: Dict) -> Dict:
        """Parse kline data into standardized format"""
        try:
            # Convert timestamp to datetime
            open_time = datetime.fromtimestamp(kline_data['t'] / 1000)
            close_time = datetime.fromtimestamp(kline_data['T'] / 1000)

            candle = {
                'Open time': open_time,
                'Close time': close_time,
                'Open': float(kline_data['o']),
                'High': float(kline_data['h']),
                'Low': float(kline_data['l']),
                'Close': float(kline_data['c']),
                'Volume': float(kline_data['v']),
                'Quote asset volume': float(kline_data['q']),
                'Number of trades': int(kline_data['n']),
                'Taker buy base asset volume': float(kline_data['V']),
                'Taker buy quote asset volume': float(kline_data['Q']),
                'interval': kline_data['i']
            }

            return candle

        except Exception as e:
            logger.error(f"Error parsing kline data: {e}")
            return {}

    def is_alive(self) -> bool:
        """Check if WebSocket connection is alive"""
        return self.is_connected and hasattr(self, 'ws_thread') and self.ws_thread.is_alive()

class BinanceDataFetcher:
    """Fetch historical data from Binance REST API"""

    def __init__(self, testnet: bool = False):
        # Use futures API endpoints instead of spot
        if testnet:
            self.base_url = "https://testnet.binancefuture.com"
        else:
            self.base_url = "https://fapi.binance.com"

    def fetch_historical_klines(self, symbol: str, interval: str = "1m",
                               limit: int = 1000) -> Optional[List[Dict]]:
        """
        Fetch historical kline data from Binance Futures API

        Args:
            symbol: Trading symbol (e.g., 'BTCUSDT')
            interval: Kline interval (1m, 5m, 15m, 1h, 1d)
            limit: Number of klines to fetch (max 1500 for futures)

        Returns:
            List of kline dictionaries or None if error
        """
        try:
            import requests

            # Use futures API endpoint
            url = f"{self.base_url}/fapi/v1/klines"
            params = {
                'symbol': symbol.upper(),
                'interval': interval,
                'limit': limit
            }

            response = requests.get(url, params=params)
            response.raise_for_status()

            data = response.json()

            # Convert to standardized format
            klines = []
            for kline in data:
                candle = {
                    'Open time': datetime.fromtimestamp(kline[0] / 1000),
                    'Open': float(kline[1]),
                    'High': float(kline[2]),
                    'Low': float(kline[3]),
                    'Close': float(kline[4]),
                    'Volume': float(kline[5]),
                    'Close time': datetime.fromtimestamp(kline[6] / 1000),
                    'Quote asset volume': float(kline[7]),
                    'Number of trades': int(kline[8]),
                    'Taker buy base asset volume': float(kline[9]),
                    'Taker buy quote asset volume': float(kline[10]),
                }
                klines.append(candle)

            logger.info(f"Fetched {len(klines)} historical futures klines for {symbol}")
            return klines

        except Exception as e:
            logger.error(f"Error fetching historical futures data: {e}")
            return None

    def save_to_csv(self, klines: List[Dict], filename: str, overwrite: bool = True):
        """
        Save klines data to CSV file

        Args:
            klines: List of kline dictionaries
            filename: Name of the CSV file to save
            overwrite: Whether to overwrite existing file (default: True)

        Returns:
            str: Full filepath if successful, None if error
        """
        try:
            import pandas as pd

            df = pd.DataFrame(klines)
            df.set_index('Open time', inplace=True)

            # Create data directory if it doesn't exist
            os.makedirs(DATA_DIR, exist_ok=True)

            filepath = os.path.join(DATA_DIR, filename)

            # Check if file exists and handle overwrite behavior
            file_exists = os.path.exists(filepath)
            if file_exists and overwrite:
                logger.info(f"Overwriting existing file: {filepath}")
            elif file_exists and not overwrite:
                logger.warning(f"File already exists and overwrite=False: {filepath}")
                return None

            # Save to CSV (this will overwrite by default)
            df.to_csv(filepath)

            if file_exists:
                logger.info(f"Overwritten {len(klines)} klines to {filepath}")
            else:
                logger.info(f"Saved {len(klines)} klines to {filepath}")

            return filepath

        except Exception as e:
            logger.error(f"Error saving to CSV: {e}")
            return None


def create_binance_hook(data_manager, strategy_engine, chart_generator, trade_engine=None):
    """
    Create a hook function for processing Binance data

    Args:
        data_manager: DataManager instance
        strategy_engine: StrategyEngine instance
        chart_generator: ChartGenerator instance
        trade_engine: Optional TradeEngine instance for live trading

    Returns:
        Callable hook function
    """

    def binance_data_hook(candle_data: Dict):
        """
        Process incoming candle data from Binance

        Args:
            candle_data: Candle data from WebSocket
        """
        try:
            symbol = candle_data.get('symbol', 'BTCUSDT')

            # Convert symbol format (BTCUSDT -> BTCUSD)
            if symbol.endswith('USDT'):
                symbol = symbol[:-1]  # Remove 'T' from 'USDT'

            # Add new candle to data manager
            success = data_manager.add_new_candle(symbol, 1, candle_data)
            if not success:
                return

            # Update higher timeframes incrementally
            df_1m = data_manager.get_dataframe(symbol, 1)
            if df_1m is not None and len(df_1m) > 0:
                latest_1m_candle = df_1m.iloc[-1]
                latest_1m_time = df_1m.index[-1]

                for timeframe in TIMEFRAMES[1:]:
                    # Check last candle in higher timeframe
                    data_manager.update_higher_timeframe_candle(symbol, timeframe, latest_1m_candle, latest_1m_time)

            # Update all analysis data for all timeframes
            for timeframe in SCANWAVE_TIMEFRAMES:
                df = data_manager.get_dataframe(symbol, timeframe)
                if df is not None and len(df) > 0:
                    latest_timestamp = df.index[-1]
                    data_manager.update_data_to_new_timestamp(symbol, latest_timestamp, timeframe)

            # Process through TradeEngine if available, otherwise use legacy analysis
            if trade_engine and trade_engine.trade_mode != "live":
                return
            # Use TradeEngine for unified processing
            trade_engine.process_candle(
                symbol=symbol,
                candle_data=candle_data,
                strategy_filter=data_manager.strategy_name
            )

            # Log current status
            latest_candle = data_manager.get_latest_candle(symbol, 1)
            if latest_candle is not None:
                logger.debug(f"Processed candle for {symbol}: Close={latest_candle['Close']}")

        except Exception as e:
            logger.error(f"Error in Binance data hook: {e}")

    return binance_data_hook

import hmac
import hashlib
import requests
from urllib.parse import urlencode

class BinanceFuturesClient:
    """Binance Futures API client for trading operations"""

    def __init__(self, api_key: str, api_secret: str, testnet: bool = False):
        self.api_key = api_key
        self.api_secret = api_secret
        self.base_url = "https://testnet.binancefuture.com" if testnet else "https://fapi.binance.com"
        self.headers = {
            'X-MBX-APIKEY': api_key
        }

        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 0.1  # Minimum 100ms between requests
        self.request_count = 0
        self.request_window_start = time.time()
        self.max_requests_per_minute = 1200  # Binance limit

    def _generate_signature(self, query_string: str) -> str:
        """Generate HMAC SHA256 signature"""
        return hmac.new(
            self.api_secret.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()

    def _rate_limit(self):
        """Implement rate limiting"""
        try:
            # Check if we need to reset the window
            current_time = time.time()
            if current_time - self.request_window_start >= 60:
                self.request_count = 0
                self.request_window_start = current_time

            # Check if we've exceeded the rate limit
            if self.request_count >= self.max_requests_per_minute:
                sleep_time = 60 - (current_time - self.request_window_start)
                if sleep_time > 0:
                    logger.warning(f"Rate limit reached, sleeping for {sleep_time:.2f}s")
                    time.sleep(sleep_time)
                    self.request_count = 0
                    self.request_window_start = time.time()

            # Ensure minimum interval between requests
            time_since_last = current_time - self.last_request_time
            if time_since_last < self.min_request_interval:
                time.sleep(self.min_request_interval - time_since_last)

            self.last_request_time = time.time()
            self.request_count += 1

        except Exception as e:
            logger.error(f"Error in rate limiting: {e}")

    def _make_request(self, method: str, endpoint: str, params: dict = None, signed: bool = False) -> Optional[dict]:
        """Make authenticated request to Binance API"""
        try:
            # Apply rate limiting
            self._rate_limit()

            url = f"{self.base_url}{endpoint}"

            if params is None:
                params = {}

            if signed:
                params['timestamp'] = int(time.time() * 1000)
                query_string = urlencode(params)
                params['signature'] = self._generate_signature(query_string)

            if method == 'GET':
                response = requests.get(url, params=params, headers=self.headers)
            elif method == 'POST':
                response = requests.post(url, params=params, headers=self.headers)
            elif method == 'DELETE':
                response = requests.delete(url, params=params, headers=self.headers)

            response.raise_for_status()
            return response.json()

        except Exception as e:
            logger.error(f"API request error: {e}")
            return None

    def get_account_info(self) -> Optional[Dict]:
        """Get futures account information"""
        return self._make_request('GET', '/fapi/v2/account', signed=True)

    def get_balance(self) -> Optional[List[Dict]]:
        """Get futures account balance"""
        account_info = self.get_account_info()
        if account_info:
            return account_info.get('assets', [])
        return None

    def get_usdt_balance(self) -> Optional[float]:
        """Get USDT balance specifically"""
        balances = self.get_balance()
        if balances:
            for balance in balances:
                if balance['asset'] == 'USDT':
                    return float(balance['walletBalance'])
        return None

    def open_position(self, symbol: str, side: str, quantity: float,
                     order_type: str = "MARKET", price: float = None,
                     time_in_force: str = "GTC") -> Optional[Dict]:
        """
        Open futures position

        Args:
            symbol: Trading symbol (e.g., 'BTCUSDT')
            side: 'BUY' or 'SELL'
            quantity: Position size
            order_type: 'MARKET' or 'LIMIT'
            price: Price for limit orders
            time_in_force: 'GTC', 'IOC', 'FOK'
        """
        params = {
            'symbol': symbol.upper(),
            'side': side.upper(),
            'type': order_type.upper(),
            'quantity': quantity
        }

        if order_type.upper() == 'LIMIT':
            if price is None:
                logger.error("Price required for limit orders")
                return None
            params['price'] = price
            params['timeInForce'] = time_in_force

        return self._make_request('POST', '/fapi/v1/order', params, signed=True)

    def set_stop_loss(self, symbol: str, side: str, quantity: float,
                     stop_price: float, price: float = None) -> Optional[Dict]:
        """
        Set stop loss order

        Args:
            symbol: Trading symbol
            side: 'BUY' or 'SELL' (opposite of position)
            quantity: Position size to close
            stop_price: Stop trigger price
            price: Limit price (optional, uses STOP_MARKET if None)
        """
        params = {
            'symbol': symbol.upper(),
            'side': side.upper(),
            'type': 'STOP_MARKET' if price is None else 'STOP',
            'quantity': quantity,
            'stopPrice': stop_price,
            'timeInForce': 'GTC'
        }

        if price is not None:
            params['price'] = price
            params['type'] = 'STOP'

        return self._make_request('POST', '/fapi/v1/order', params, signed=True)

    def set_take_profit(self, symbol: str, side: str, quantity: float,
                       price: float, time_in_force: str = "GTC") -> Optional[Dict]:
        """
        Set take profit limit order

        Args:
            symbol: Trading symbol
            side: 'BUY' or 'SELL' (opposite of position)
            quantity: Position size to close
            price: Take profit price
            time_in_force: Order time in force
        """
        params = {
            'symbol': symbol.upper(),
            'side': side.upper(),
            'type': 'LIMIT',
            'quantity': quantity,
            'price': price,
            'timeInForce': time_in_force
        }

        return self._make_request('POST', '/fapi/v1/order', params, signed=True)

    def get_open_positions(self) -> Optional[List[Dict]]:
        """Get all open positions"""
        account_info = self.get_account_info()
        if account_info:
            positions = account_info.get('positions', [])
            return [pos for pos in positions if float(pos['positionAmt']) != 0]
        return None

    def get_open_orders(self, symbol: str = None) -> Optional[List[Dict]]:
        """Get open orders"""
        params = {}
        if symbol:
            params['symbol'] = symbol.upper()

        return self._make_request('GET', '/fapi/v1/openOrders', params, signed=True)

    def cancel_order(self, symbol: str, order_id: int) -> Optional[Dict]:
        """Cancel an order"""
        params = {
            'symbol': symbol.upper(),
            'orderId': order_id
        }

        return self._make_request('DELETE', '/fapi/v1/order', params, signed=True)

    def close_position(self, symbol: str, quantity: float = None) -> Optional[Dict]:
        """
        Close position using market order

        Args:
            symbol: Trading symbol
            quantity: Quantity to close (None for full position)
        """
        if quantity is None:
            # Get current position size
            positions = self.get_open_positions()
            if not positions:
                return None

            position = next((p for p in positions if p['symbol'] == symbol.upper()), None)
            if not position:
                return None

            quantity = abs(float(position['positionAmt']))
            side = 'SELL' if float(position['positionAmt']) > 0 else 'BUY'
        else:
            # Determine side based on current position
            positions = self.get_open_positions()
            position = next((p for p in positions if p['symbol'] == symbol.upper()), None)
            if not position:
                return None

            side = 'SELL' if float(position['positionAmt']) > 0 else 'BUY'

        return self.open_position(symbol, side, quantity, "MARKET")

    def get_current_price(self, symbol: str) -> Optional[float]:
        """Get current market price for a symbol"""
        try:
            response = self._make_request('GET', '/fapi/v1/ticker/price', {'symbol': symbol.upper()})
            if response:
                return float(response['price'])
            return None
        except Exception as e:
            logger.error(f"Error getting current price: {e}")
            return None

    def set_leverage(self, symbol: str, leverage: int) -> Optional[Dict]:
        """
        Set leverage for a symbol

        Args:
            symbol: Trading symbol
            leverage: Leverage value (1-125)
        """
        params = {
            'symbol': symbol.upper(),
            'leverage': leverage
        }
        return self._make_request('POST', '/fapi/v1/leverage', params, signed=True)

    def calculate_position_size(self, symbol: str, balance: float, risk_per_trade: float,
                               entry_price: float, stop_loss: float, leverage: int = 1) -> float:
        """
        Calculate position size based on risk management

        Args:
            symbol: Trading symbol
            balance: Account balance in USDT
            risk_per_trade: Risk percentage (e.g., 0.02 for 2%)
            entry_price: Entry price
            stop_loss: Stop loss price
            leverage: Leverage to use

        Returns:
            Position size in base currency
        """
        try:
            # Calculate risk amount in USDT
            risk_amount = balance * risk_per_trade

            # Calculate stop distance as percentage
            stop_distance = abs(entry_price - stop_loss) / entry_price

            # Calculate position size in USDT
            position_size_usdt = risk_amount / stop_distance

            # Apply leverage
            position_size_usdt = position_size_usdt * leverage

            # Convert to base currency quantity
            quantity = position_size_usdt / entry_price

            # Get symbol info for precision
            symbol_info = self.get_symbol_info(symbol)
            if symbol_info:
                # Round to appropriate precision
                quantity_precision = symbol_info.get('quantityPrecision', 3)
                quantity = round(quantity, quantity_precision)

            return quantity

        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            return 0.0

    def get_symbol_info(self, symbol: str) -> Optional[Dict]:
        """Get symbol trading rules and info"""
        try:
            response = self._make_request('GET', '/fapi/v1/exchangeInfo')
            if response and 'symbols' in response:
                for sym in response['symbols']:
                    if sym['symbol'] == symbol.upper():
                        return sym
            return None
        except Exception as e:
            logger.error(f"Error getting symbol info: {e}")
            return None

    def place_order_with_sl_tp(self, symbol: str, side: str, quantity: float,
                               stop_loss: float, take_profit: float,
                               leverage: int = None) -> Optional[Dict]:
        """
        Place market order with stop loss and take profit

        Args:
            symbol: Trading symbol
            side: 'BUY' or 'SELL'
            quantity: Position size
            stop_loss: Stop loss price
            take_profit: Take profit price
            leverage: Optional leverage to set

        Returns:
            Dict with order results or None if error
        """
        try:
            symbol_upper = symbol.upper()

            # Set leverage if specified
            if leverage:
                leverage_result = self.set_leverage(symbol_upper, leverage)
                if not leverage_result:
                    logger.error(f"Failed to set leverage to {leverage}")
                    return None

            # Place market order
            market_order = self.open_position(symbol_upper, side, quantity, "MARKET")
            if not market_order:
                logger.error("Failed to place market order")
                return None

            logger.info(f"Market order placed: {market_order}")

            # Determine SL and TP sides (opposite of entry)
            sl_tp_side = 'SELL' if side == 'BUY' else 'BUY'

            # Place stop loss order
            sl_order = self.set_stop_loss(symbol_upper, sl_tp_side, quantity, stop_loss)
            if not sl_order:
                logger.warning("Failed to place stop loss order")

            # Place take profit order
            tp_order = self.set_take_profit(symbol_upper, sl_tp_side, quantity, take_profit)
            if not tp_order:
                logger.warning("Failed to place take profit order")

            return {
                'market_order': market_order,
                'stop_loss_order': sl_order,
                'take_profit_order': tp_order,
                'symbol': symbol_upper,
                'side': side,
                'quantity': quantity
            }

        except Exception as e:
            logger.error(f"Error placing order with SL/TP: {e}")
            return None

    def cancel_all_orders(self, symbol: str) -> Optional[Dict]:
        """Cancel all open orders for a symbol"""
        params = {'symbol': symbol.upper()}
        return self._make_request('DELETE', '/fapi/v1/allOpenOrders', params, signed=True)

    def get_position_info(self, symbol: str) -> Optional[Dict]:
        """Get detailed position information for a symbol"""
        try:
            positions = self.get_open_positions()
            if positions:
                for pos in positions:
                    if pos['symbol'] == symbol.upper():
                        return pos
            return None
        except Exception as e:
            logger.error(f"Error getting position info: {e}")
            return None
