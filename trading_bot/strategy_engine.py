"""
Strategy engine for pattern recognition and signal generation
"""

import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
import logging

from .config import *
from .technical_analysis import *
from .wave_analysis import *
from .level_analysis import *

logger = logging.getLogger(__name__)

class StrategyResult:
    """Standardized strategy result format"""

    def __init__(self, pattern_name: str = None, signal_strength: float = 0.0,
                 entry_price: float = None, stop_loss: float = None,
                 take_profit: float = None, risk_reward: float = None,
                 entry_now: bool = False, metadata: Dict = None):
        self.pattern_name = pattern_name
        self.signal_strength = signal_strength
        self.entry_price = entry_price
        self.stop_loss = stop_loss
        self.take_profit = take_profit
        self.risk_reward = risk_reward
        self.entry_now = entry_now
        self.metadata = metadata or {}

    def is_valid(self) -> bool:
        """Check if strategy result is valid"""
        return (self.pattern_name is not None and
                self.entry_price is not None and
                self.stop_loss is not None and
                self.take_profit is not None)

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'pattern_name': self.pattern_name,
            'signal_strength': self.signal_strength,
            'entry_price': self.entry_price,
            'stop_loss': self.stop_loss,
            'take_profit': self.take_profit,
            'risk_reward': self.risk_reward,
            'entry_now': self.entry_now,
            'metadata': self.metadata
        }

class StrategyEngine:
    """Main strategy engine for pattern recognition"""

    def __init__(self, data_manager):
        self.data_manager = data_manager
        self.strategies = {
            'correction_broken': self._correction_broken_strategy,
            'zanshin1': self._zanshin1_strategy,
            'zanshin2': self._zanshin2_strategy,
            'bos': self._bos_strategy,
            'break_range_reverse': self._break_range_reverse_strategy,
            'smc': self._smc_strategy,
            'confluent_key': self._confluent_key_strategy,
            'range_fake_breakout': self._range_fake_breakout_strategy,
        }

    def analyze_coc_patterns(self, name: str, main_tf: int, high_tf: int, very_high_tf: int) -> StrategyResult:
        """
        Analyze Change of Character (COC) patterns

        Args:
            name: Symbol name
            main_tf: Main timeframe
            high_tf: Higher timeframe
            very_high_tf: Very high timeframe

        Returns:
            StrategyResult: Standardized result format
        """
        try:
            # Get current market data
            df = self.data_manager.get_dataframe(name, main_tf)
            waves = self.data_manager.waves[name][main_tf]
            primary_level = self.data_manager.primary_level[name][main_tf]

            if df is None or df.empty or not waves or not primary_level:
                return StrategyResult()

            # Check each strategy pattern
            for pattern_name, strategy_func in self.strategies.items():
                try:
                    result = strategy_func(name, main_tf, high_tf, very_high_tf)
                    if result and result.is_valid():
                        logger.info(f"Pattern detected: {pattern_name} for {name}")
                        return result
                except Exception as e:
                    logger.error(f"Error in strategy {pattern_name}: {e}")
                    continue

            return StrategyResult()

        except Exception as e:
            logger.error(f"Error analyzing COC patterns: {e}")
            return StrategyResult()

    def analyze_specific_strategy(self, name: str, main_tf: int, high_tf: int, very_high_tf: int,
                                 strategy_name: str) -> StrategyResult:
        """
        Analyze a specific strategy pattern

        Args:
            name: Symbol name
            main_tf: Main timeframe
            high_tf: Higher timeframe
            very_high_tf: Very high timeframe
            strategy_name: Name of the specific strategy to test

        Returns:
            StrategyResult: Result from the specific strategy
        """
        try:
            # Get current market data
            df = self.data_manager.get_dataframe(name, main_tf)
            waves = self.data_manager.waves[name][main_tf]
            primary_level = self.data_manager.primary_level[name][main_tf]

            if df is None or df.empty or not waves or not primary_level:
                return StrategyResult()

            # Check if strategy exists
            if strategy_name not in self.strategies:
                logger.error(f"Strategy '{strategy_name}' not found. Available strategies: {list(self.strategies.keys())}")
                return StrategyResult()

            # Run the specific strategy
            strategy_func = self.strategies[strategy_name]
            try:
                result = strategy_func(name, main_tf, high_tf, very_high_tf)
                if result and result.is_valid():
                    logger.info(f"Strategy '{strategy_name}' generated signal for {name}")
                    return result
                else:
                    return StrategyResult()
            except Exception as e:
                logger.error(f"Error in strategy '{strategy_name}': {e}")
                return StrategyResult()

        except Exception as e:
            logger.error(f"Error analyzing specific strategy '{strategy_name}': {e}")
            return StrategyResult()

    def get_available_strategies(self) -> List[str]:
        """Get list of available strategy names"""
        return list(self.strategies.keys())

    def _correction_broken_strategy(self, name: str, main_tf: int, high_tf: int, very_high_tf: int) -> Optional[StrategyResult]:
        """Correction broken strategy"""
        try:
            df = self.data_manager.df[name][main_tf]
            waves = self.data_manager.waves[name][main_tf]
            primary_level = self.data_manager.primary_level[name][main_tf]
            history = self.data_manager.history[name][main_tf]

            # Check if correction is broken
            reason = self._is_correction_broken(name, high_tf, primary_level, waves, history)
            if not reason:
                return None

            # Calculate entry, SL, TP
            entry_price, sl, tp, rr = self._calculate_entry_sl_tp_correction_broken(df, waves, primary_level, history, reason)

            if not entry_price or not sl or not tp:
                raise ValueError('Invalid entry/SL/TP for new signal correction broken')

            return StrategyResult(
                pattern_name='correction_broken',
                signal_strength=None,
                entry_price=entry_price,
                stop_loss=sl,
                take_profit=tp,
                risk_reward=rr,
                entry_now=COC_PATTERN_POINT['correction_broken']['entry_now'],
                metadata={
                    'ratio_trend': primary_level['ratio_trend'],
                    'reason': reason
                }
            )

        except Exception as e:
            logger.error(f"Error in correction broken strategy: {e}")
            return None

    def _zanshin1_strategy(self, name: str, main_tf: int, high_tf: int, very_high_tf: int) -> Optional[StrategyResult]:
        """Zanshin 1 strategy"""
        try:
            # Implementation for Zanshin 1 pattern
            # This is a placeholder - implement specific logic
            return None
        except Exception as e:
            logger.error(f"Error in zanshin1 strategy: {e}")
            return None

    def _zanshin2_strategy(self, name: str, main_tf: int, high_tf: int, very_high_tf: int) -> Optional[StrategyResult]:
        """Zanshin 2 strategy"""
        try:
            # Implementation for Zanshin 2 pattern
            # This is a placeholder - implement specific logic
            return None
        except Exception as e:
            logger.error(f"Error in zanshin2 strategy: {e}")
            return None

    def _bos_strategy(self, name: str, main_tf: int, high_tf: int, very_high_tf: int) -> Optional[StrategyResult]:
        """Break of Structure (BOS) strategy"""
        try:
            df = self.data_manager.get_dataframe(name, main_tf)
            waves = self.data_manager.waves[name][main_tf]
            primary_level = self.data_manager.primary_level[name][main_tf]

            # Check BOS conditions
            if not self._is_bos_detected(df, waves, primary_level):
                return None

            # Calculate entry, SL, TP
            location = location_compare_level(primary_level)
            entry_price, sl, tp, rr = self._calculate_entry_sl_tp(df, waves, primary_level)

            if not entry_price or not sl or not tp:
                return None

            signal_strength = self._calculate_signal_strength(location, 'bos', None)

            return StrategyResult(
                pattern_name='bos',
                signal_strength=signal_strength,
                entry_price=entry_price,
                stop_loss=sl,
                take_profit=tp,
                risk_reward=rr,
                entry_now=COC_PATTERN_POINT['bos']['entry_now'],
                metadata={
                    'location': location,
                    'timeframe': main_tf,
                    'symbol': name
                }
            )

        except Exception as e:
            logger.error(f"Error in BOS strategy: {e}")
            return None

    def _break_range_reverse_strategy(self, name: str, main_tf: int, high_tf: int, very_high_tf: int) -> Optional[StrategyResult]:
        """Break range reverse strategy"""
        try:
            # Implementation for break range reverse pattern
            # This is a placeholder - implement specific logic
            return None
        except Exception as e:
            logger.error(f"Error in break range reverse strategy: {e}")
            return None

    def _smc_strategy(self, name: str, main_tf: int, high_tf: int, very_high_tf: int) -> Optional[StrategyResult]:
        """Smart Money Concepts (SMC) strategy"""
        try:
            # Implementation for SMC pattern
            # This is a placeholder - implement specific logic
            return None
        except Exception as e:
            logger.error(f"Error in SMC strategy: {e}")
            return None

    def _confluent_key_strategy(self, name: str, main_tf: int, high_tf: int, very_high_tf: int) -> Optional[StrategyResult]:
        """Confluent key strategy"""
        try:
            # Implementation for confluent key pattern
            # This is a placeholder - implement specific logic
            return None
        except Exception as e:
            logger.error(f"Error in confluent key strategy: {e}")
            return None



    def _range_fake_breakout_strategy(self, name: str, main_tf: int, high_tf: int, very_high_tf: int) -> Optional[StrategyResult]:
        """Range fake breakout strategy"""
        try:
            # Implementation for range fake breakout pattern
            # This is a placeholder - implement specific logic
            return None
        except Exception as e:
            logger.error(f"Error in range fake breakout strategy: {e}")
            return None

    def _is_correction_broken(self, name: str, high_tf: int, primary_level: Dict, waves: List[Dict], history: Dict) -> bool:
        """Check if correction is broken"""
        try:
            # Implement correction broken logic based on wave analysis
            if not waves or len(waves) < 2:
                return False

            high_primary_level = self.data_manager.primary_level[name][high_tf]

            # Check if latest wave breaks the correction structure
            latest_wave = waves[-1]
            if not latest_wave['confirmed']:
                return False

            if len(history['recent_level']) > 1:
                if history['recent_level'][-1]['label'] != history['recent_level'][-2]['label'] and \
                    history['recent_level'][-1]['checked_index'] == latest_wave['checked_index'] and \
                    history['recent_level'][-1]['label'] != primary_level['label']:
                        logger.info('Correction broken recent level signal')
                        return f"recent level broken False direction"

            if len(history['sub_primary_level']) > 1:
                if history['sub_primary_level'][-1]['label'] != history['sub_primary_level'][-2]['label'] and \
                    history['sub_primary_level'][-1]['checked_index'] == latest_wave['checked_index']:
                        if history['sub_primary_level'][-1]['label'] != primary_level['label']:
                            logger.info('Correction broken sub primary level signal')
                            return f"sub primary level broken False direction"
                        elif primary_level['ratio_trend'] >= 0.7:
                            logger.info('Correction broken sub primary level signal')
                            return f"sub primary level broken True direction"
            return False

        except Exception as e:
            logger.error(f"Error checking correction broken: {e}")
            return False

    def _is_bos_detected(self, df: pd.DataFrame, waves: List[Dict], primary_level: Dict) -> bool:
        """Check if Break of Structure is detected"""
        try:
            # Implement BOS detection logic
            if not waves or len(waves) < 2:
                return False

            # Check for structure break
            latest_wave = waves[-1]
            if not latest_wave['confirmed']:
                return False

            # Check if price breaks previous structure
            if primary_level['label'] == 1:
                # For uptrend, check if we break previous low
                return df.loc[latest_wave['peak_index']]['Low'] < df.loc[primary_level['key_level_index']]['Low']
            else:
                # For downtrend, check if we break previous high
                return df.loc[latest_wave['peak_index']]['High'] > df.loc[primary_level['key_level_index']]['High']

        except Exception as e:
            logger.error(f"Error detecting BOS: {e}")
            return False

    def _calculate_entry_sl_tp_correction_broken(self, df: pd.DataFrame, waves: List[Dict], primary_level: Dict, history: Dict, reason: str) -> Tuple[float, float, float, float]:
        """Calculate entry, stop loss, and take profit"""
        try:
            if not waves:
                return None, None, None, None

            latest_candle = df.loc[waves[-1]['checked_index']]
            entry_price = latest_candle['Close']

            if primary_level['label'] == 1:
                direction_compare = 1
                current_low = df.loc[latest_candle.name]['Low']
                tp_funct = min
            else:
                direction_compare = -1
                current_low = df.loc[latest_candle.name]['High']
                tp_funct = max

            # Calculate stop loss
            if reason == "sub primary level broken True direction":
                sl = df.loc[history['sub_primary_level'][0]['key_level_index']]['Close'] - latest_candle['ma_candle'] * RATIO_MA_SL * direction_compare * 2
                tp = df.loc[primary_level['key_level_index']]['Close'] + abs(df.loc[primary_level['peak_index']]['Close'] - df.loc[primary_level['key_level_index']]['Close'])*direction_compare*0.8
            else:
                sl = current_low - latest_candle['ma_candle'] * RATIO_MA_SL * direction_compare
                if reason == "recent level broken False direction":
                    tp = tp_funct(df.loc[history['recent_level'][-1]['key_level_index']]['Close'], df.loc[history['recent_level'][-1]['key_level_index']]['Open'])
                else:
                    tp = tp_funct(df.loc[history['sub_primary_level'][-1]['key_level_index']]['Close'], df.loc[history['sub_primary_level'][-1]['key_level_index']]['Open'])

            # Calculate risk-reward ratio
            rr = tp/sl

            return entry_price, sl, tp, rr

        except Exception as e:
            logger.error(f"Error calculating entry/SL/TP: {e}")
            return None, None, None, None

    def _calculate_signal_strength(self, location: str, pattern: str, candle_pattern: str) -> float:
        """Calculate signal strength based on location and patterns"""
        try:
            strength = 0.0

            # Location points
            if location in LOCATION_POINT:
                strength += LOCATION_POINT[location]

            # Pattern points
            if pattern in COC_PATTERN_POINT:
                strength += COC_PATTERN_POINT[pattern]['point']

            # Candle pattern points
            if candle_pattern and candle_pattern in CANDLES_PATTERN_POINT:
                strength += CANDLES_PATTERN_POINT[candle_pattern]['point']

            return strength

        except Exception as e:
            logger.error(f"Error calculating signal strength: {e}")
            return 0.0
